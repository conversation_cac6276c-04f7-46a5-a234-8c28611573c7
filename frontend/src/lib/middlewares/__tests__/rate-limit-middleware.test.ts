/**
 * Test file for rate-limit-middleware.ts
 *
 * This file tests the rate limiting middleware functionality
 * with proper mocking of NextResponse and auth helpers.
 */

// Mock next/server before importing anything else
jest.mock('next/server', () => {
  // Create a mock Response class with json method
  class MockResponse {
    status: number;
    headers: Headers;
    body: any;

    constructor(body?: any, init?: ResponseInit) {
      this.status = init?.status || 200;
      this.headers = new Headers(init?.headers);
      this.body = body;
    }

    json() {
      return Promise.resolve(this.body ? JSON.parse(this.body) : {});
    }

    text() {
      return Promise.resolve(this.body ? String(this.body) : '');
    }
  }

  // Create a mock NextResponse with static json method
  const MockNextResponse = {
    json: jest.fn((body, init) => {
      return {
        status: init?.status || 200,
        headers: new Headers(init?.headers),
        body: JSON.stringify(body),
        json: () => Promise.resolve(body)
      };
    })
  };

  return {
    NextRequest: jest.fn().mockImplementation(() => ({})),
    NextResponse: MockNextResponse
  };
});

// Now import the modules we need
import { NextRequest } from 'next/server';
import { withRateLimit } from '../rate-limit-middleware';
import { RateLimitService } from '@/lib/services/rate-limit-service';
import { AuthUser, UserRole } from '@/lib/auth';
import { SupabaseClient } from '@supabase/supabase-js';

// Mock Supabase client
const mockSupabase = {
  auth: {
    getUser: jest.fn().mockResolvedValue({
      data: { user: { id: 'user-123', email: '<EMAIL>' } },
      error: null
    }),
    getSession: jest.fn().mockResolvedValue({
      data: {
        session: {
          user: { id: 'user-123', email: '<EMAIL>' },
          access_token: 'mock-token'
        }
      },
      error: null
    }),
  },
  storage: {
    from: jest.fn().mockReturnThis(),
    upload: jest.fn().mockResolvedValue({ data: { path: 'test/path' }, error: null }),
  },
};

// Mock @supabase/ssr
jest.mock('@supabase/ssr', () => ({
  createServerClient: jest.fn(() => mockSupabase),
  createBrowserClient: jest.fn(),
  parseCookieHeader: jest.fn(),
  serializeCookieHeader: jest.fn(),
}));

// Mock RateLimitService
const mockCanUploadDocument = jest.fn();
const mockTrackDocumentUpload = jest.fn();
jest.mock('@/lib/services/rate-limit-service', () => ({
  RateLimitService: jest.fn().mockImplementation(() => ({
    canUploadDocument: mockCanUploadDocument,
    trackDocumentUpload: mockTrackDocumentUpload,
  })),
}));

// Mock auth-helpers
jest.mock('@/lib/auth', () => {
  const mockUser: AuthUser = {
    id: 'user-123',
    email: '<EMAIL>',
    role: UserRole.Partner, // Using the enum value instead of string
    tenantId: 'tenant-123',
    exp: Math.floor(Date.now() / 1000) + 3600
  };

  return {
    withAuth: jest.fn((handler) => {
      return async (req: NextRequest) => {
        try {
          return await handler(req, mockUser, mockSupabase);
        } catch (error) {
          const { NextResponse } = require('next/server');
          return NextResponse.json(
            { error: 'Auth error' },
            { status: 401 }
          );
        }
      };
    }),
    requireAuth: jest.fn().mockReturnValue(mockUser)
  };
});

describe('Rate Limit Middleware', () => {
  // Setup test variables
  let mockReq: NextRequest;
  let mockHandler: jest.Mock;
  let wrappedHandler: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create a mock request with both formData and json methods
    mockReq = {
      method: 'POST',
      headers: {
        get: jest.fn().mockImplementation((name) => {
          if (name.toLowerCase() === 'content-type') return 'multipart/form-data';
          return null;
        }),
      },
      formData: jest.fn().mockResolvedValue(new FormData()),
      json: jest.fn().mockResolvedValue({ fileSize: 0 }),
      nextUrl: { pathname: '/api/test' },
      url: 'http://localhost:3000/api/test',
      cookies: {
        get: jest.fn(),
        getAll: jest.fn().mockReturnValue([]),
      },
    } as unknown as NextRequest;

    // Create a mock handler that returns a successful response
    mockHandler = jest.fn().mockResolvedValue({
      status: 200,
      json: jest.fn().mockResolvedValue({ success: true }),
    });

    // Default behavior for rate limit service
    mockCanUploadDocument.mockResolvedValue({ allowed: true, reason: '' });

    // Create the wrapped handler
    wrappedHandler = withRateLimit(mockHandler);
  });

  it('should allow document upload within rate limit', async () => {
    // Configure rate limit service to allow the upload
    mockCanUploadDocument.mockResolvedValue({ allowed: true, reason: '' });

    // Call the wrapped handler
    const response = await wrappedHandler(mockReq);

    // Verify response
    expect(response.status).toBe(200);
    // @ts-ignore - Jest types issue with .resolves
    await expect(response.json()).resolves.toEqual({ success: true });

    // Verify service calls
    expect(mockCanUploadDocument).toHaveBeenCalledWith('tenant-123', 0);
    expect(mockTrackDocumentUpload).toHaveBeenCalledWith('tenant-123', 0);
    expect(mockHandler).toHaveBeenCalled();
  });

  it('should reject document upload when rate limit is exceeded', async () => {
    // Configure rate limit service to reject the upload
    mockCanUploadDocument.mockResolvedValue({ allowed: false, reason: 'Quota exceeded' });

    // Call the wrapped handler
    const response = await wrappedHandler(mockReq);

    // Verify response indicates rate limit exceeded
    expect(response.status).toBe(429);
    // @ts-ignore - Jest types issue with .resolves
    await expect(response.json()).resolves.toEqual({
      error: 'Rate limit exceeded: Quota exceeded'
    });

    // Verify service calls
    expect(mockCanUploadDocument).toHaveBeenCalledWith('tenant-123', 0);
    expect(mockTrackDocumentUpload).not.toHaveBeenCalled();
    expect(mockHandler).not.toHaveBeenCalled();
  });

  it('should handle formData extraction for file size', async () => {
    // Set up mock form data with a file
    const mockFile = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const mockFormData = new FormData();
    mockFormData.append('file', mockFile);
    mockReq.formData = jest.fn().mockResolvedValue(mockFormData);

    // Call the wrapped handler
    await wrappedHandler(mockReq);

    // Verify file size was extracted correctly (content length = 12 bytes)
    expect(mockCanUploadDocument).toHaveBeenCalledWith('tenant-123', 12);
  });

  it('should handle json extraction for file size', async () => {
    // Set up request to return json instead of formData
    mockReq.headers.get = jest.fn().mockImplementation((name) => {
      if (name.toLowerCase() === 'content-type') return 'application/json';
      return null;
    });

    mockReq.json = jest.fn().mockResolvedValue({ fileSize: 1024 });

    // Call the wrapped handler
    await wrappedHandler(mockReq);

    // Verify file size was extracted correctly from JSON
    expect(mockCanUploadDocument).toHaveBeenCalledWith('tenant-123', 1024);
  });

  it('should handle missing tenant ID gracefully', async () => {
    // Set up auth to return user without tenant ID
    jest.requireMock('@/lib/auth').requireAuth.mockReturnValueOnce({
      id: 'user-123',
      email: '<EMAIL>',
      role: UserRole.Partner,
      tenantId: null,
      exp: Math.floor(Date.now() / 1000) + 3600
    });

    // Call the wrapped handler
    const response = await wrappedHandler(mockReq);

    // Verify response still works
    expect(response.status).toBe(200);

    // No tenant ID, so these shouldn't be called
    expect(mockCanUploadDocument).not.toHaveBeenCalled();
    expect(mockTrackDocumentUpload).not.toHaveBeenCalled();
    expect(mockHandler).toHaveBeenCalled();
  });

  it('should bypass rate limit checks for system admin roles', async () => {
    // Set up auth to return user with admin role
    jest.requireMock('@/lib/auth').requireAuth.mockReturnValueOnce({
      id: 'admin-123',
      email: '<EMAIL>',
      role: UserRole.Superadmin,
      tenantId: 'tenant-123',
      exp: Math.floor(Date.now() / 1000) + 3600
    });

    // Call the wrapped handler
    const response = await wrappedHandler(mockReq);

    // Verify response
    expect(response.status).toBe(200);

    // Admin users bypass rate limits
    expect(mockCanUploadDocument).not.toHaveBeenCalled();
    expect(mockTrackDocumentUpload).toHaveBeenCalledWith('tenant-123', 0);
    expect(mockHandler).toHaveBeenCalled();
  });
});
