/**
 * Authentication Types
 * Centralized type definitions for the authentication system
 */

/**
 * Represents the possible roles a user can have within the system.
 * Enum members are PascalCase (for linting), while string values are lowercase (for DB/JWT).
 */
export enum UserRole {
  Authenticated = 'authenticated',
  Superadmin = 'superadmin',
  Partner = 'partner',
  Attorney = 'attorney',
  Paralegal = 'paralegal',
  Staff = 'staff',
  Client = 'client',
  Admin = 'admin',
}

/**
 * Authenticated user information
 * Represents the user data available after authentication
 */
export interface AuthUser {
  /**
   * User's unique identifier
   */
  id: string;

  /**
   * User's email address
   */
  email: string;

  /**
   * User's role in the system
   */
  role: UserRole;

  /**
   * Tenant identifier (for multi-tenancy)
   */
  tenantId: string | null;

  /**
   * Optional metadata
   */
  metadata?: Record<string, any>;

  /**
   * Optional token expiration timestamp
   */
  exp?: number;

  /**
   * Optional first name
   */
  firstName?: string;

  /**
   * Optional last name
   */
  lastName?: string;
}

/**
 * Type guard to check if a string is a valid UserRole
 *
 * @param role The role to check
 * @returns True if the role is a valid UserRole
 */
export function isValidUserRole(role: unknown): role is UserRole {
  return typeof role === 'string' &&
    ['authenticated', 'superadmin', 'partner', 'attorney', 'paralegal', 'staff', 'client', 'admin'].includes(role as string);
}

/**
 * Authentication result
 * Represents the result of an authentication operation
 */
export interface AuthResult {
  /**
   * Whether the authentication was successful
   */
  success: boolean;

  /**
   * The authenticated user (if successful)
   */
  user?: AuthUser;

  /**
   * Error message (if unsuccessful)
   */
  error?: string;
}

/**
 * JWT Claims (Tenant-aware)
 * Represents the claims in a JWT token with tenant isolation
 */
export interface TenantClaims {
  /**
   * Subject (user ID)
   */
  sub: string;

  /**
   * Email address
   */
  email?: string;

  /**
   * Roles
   */
  roles?: UserRole[];

  /**
   * Role (singular) - primary role from JWT root claims
   */
  role?: UserRole;

  /**
   * Tenant ID - critical for multi-tenant isolation
   */
  tenant_id?: string;

  /**
   * Expiration time
   */
  exp?: number;

  /**
   * Issued at time
   */
  iat?: number;

  /**
   * JWT ID
   */
  jti?: string;

  /**
   * Additional claims
   */
  [key: string]: any;
}

/**
 * Session Extension
 * Extended session information with tenant context
 */
export interface SessionExt {
  user: AuthUser;
  tenantId: string | null;
  role: UserRole;
  exp?: number;
}

/**
 * Route Handler with Authentication
 * Type for authenticated route handlers
 */
export type AuthRouteHandler = (
  req: import('next/server').NextRequest,
  user: AuthUser,
  supabase: import('@supabase/supabase-js').SupabaseClient<import('../supabase/database.types').Database>,
  context: Record<string, any>
) => Promise<Response>;

/**
 * Super Admin Email List
 * Hardcoded list of super admin emails for platform-level access
 */
export const SUPER_ADMIN_EMAILS = [
  '<EMAIL>'
] as const;

/**
 * Type for super admin emails
 */
export type SuperAdminEmail = typeof SUPER_ADMIN_EMAILS[number];

// Legacy types for backward compatibility
import { User, Session } from '@supabase/supabase-js';

// Types for user profile (legacy)
export interface UserProfile {
  id: string;
  auth_id: string;
  email: string;
  first_name: string | null;
  last_name: string | null;
  role: string;
  tenant_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Types for our auth context (legacy)
export interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>;
  signOut: () => Promise<void>;
  signUp: (email: string, password: string, metadata?: Record<string, any>) => Promise<{ error: Error | null }>;
  resetPassword: (email: string) => Promise<{ error: Error | null }>;
  updatePassword: (password: string) => Promise<{ error: Error | null }>;
  refreshSession: () => Promise<void>;
}
