/**
 * Session Management
 * Handles user sessions, authentication, and route protection
 */

import { NextRequest, NextResponse } from 'next/server';
import { Session } from '@supabase/supabase-js';
import { createClient, createServerClientForUser, createServiceClient } from './client';
import { <PERSON>th<PERSON><PERSON>, <PERSON>r<PERSON><PERSON>, <PERSON>ant<PERSON><PERSON>ms, AuthRouteHandler, SessionExt } from './types';
import { parseJwtPayload, type JwtPayload } from '../supabase/client';
import { enhanceClientWithSchemas } from '../supabase/schema-client';
import { logSecurityEvent } from '../security/forensics';
import { getUnifiedSession, type UnifiedSession } from './getUnifiedSession';
import { Database } from '../supabase/database.types';
import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Gets the current server session using unified session approach
 * Supports both Supabase Auth and legacy cookie/JWT authentication
 *
 * @returns The current session or null if not authenticated
 */
export async function getServerSession(): Promise<Session | null> {
  try {
    // Try unified session first (supports feature flag switching)
    const unifiedSession = await getUnifiedSession();
    if (unifiedSession) {
      // Convert unified session to Supabase session format for compatibility
      return {
        access_token: unifiedSession.access_token,
        refresh_token: unifiedSession.refresh_token || '',
        expires_in: unifiedSession.expires_in || 3600,
        expires_at: unifiedSession.expires_at,
        token_type: unifiedSession.token_type || 'bearer',
        user: unifiedSession.user
      } as Session;
    }

    // Fallback to direct Supabase session
    const supabase = await createClient();
    const { data: { session }, error } = await supabase.auth.getSession();

    if (error) {
      console.error('Session error:', error);
      return null;
    }

    return session;
  } catch (error) {
    console.error('Error getting server session:', error);
    return null;
  }
}

/**
 * Gets the unified session directly
 * This is the new preferred method for session management
 *
 * @returns The unified session or null if not authenticated
 */
export async function getUnifiedServerSession(): Promise<UnifiedSession | null> {
  try {
    return await getUnifiedSession();
  } catch (error) {
    console.error('Error getting unified session:', error);
    return null;
  }
}

/**
 * Gets the current authenticated user
 * 
 * @returns The current user or null if not authenticated
 */
export async function getUser(): Promise<AuthUser | null> {
  try {
    const session = await getServerSession();
    if (!session) return null;
    
    const supabase = await createClient();
    return createAuthUserFromSession(supabase, session);
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

/**
 * Requires authentication and returns the user
 * Throws an error if not authenticated
 * 
 * @param allowedRoles Optional array of allowed roles
 * @returns The authenticated user
 * @throws Error if not authenticated or insufficient permissions
 */
export async function requireAuth(allowedRoles?: UserRole[]): Promise<AuthUser> {
  const user = await getUser();
  
  if (!user) {
    throw new Error('Authentication required');
  }
  
  if (allowedRoles && allowedRoles.length > 0) {
    if (!allowedRoles.includes(user.role)) {
      throw new Error(`Insufficient permissions. Required roles: ${allowedRoles.join(', ')}`);
    }
  }
  
  return user;
}

/**
 * Creates an AuthUser object from a Supabase session
 * Handles JWT parsing and tenant isolation
 *
 * @param supabase The Supabase client
 * @param session The Supabase session
 * @returns The AuthUser object or null if session is invalid
 */
export function createAuthUserFromSession(
  supabase: SupabaseClient<Database>,
  session: Session | null
): AuthUser | null {
  if (!session?.user || !session.access_token) {
    logSecurityEvent(supabase, 'auth.session_missing', { 
      message: 'No active session or access token found in createAuthUserFromSession.'
    });
    return null;
  }

  const userFromSession = session.user;
  const token = session.access_token;
  let role: UserRole = UserRole.Client; // Use Client as default
  let tenantId: string | null = null;
  let email: string | undefined | null = userFromSession.email;

  try {
    const decoded: JwtPayload | null = parseJwtPayload(token);
    if (decoded) {
      // Validate required claims from JWT
      if (!decoded.sub) throw new Error('JWT missing required claim: sub');
      if (!decoded.email) throw new Error('JWT missing required claim: email');
      if (!decoded.role) throw new Error('JWT missing required claim: role');

      const roleFromToken = decoded.role as string;
      if (isValidUserRole(roleFromToken)) {
        role = roleFromToken as UserRole;
      } else {
        logSecurityEvent(supabase, 'auth.jwt_invalid_role', { 
          message: `Invalid role found in JWT: ${roleFromToken}. Defaulting to ${role}.`, 
          userId: decoded.sub 
        });
      }
      tenantId = decoded.tenant_id ?? null;
      email = decoded.email;

      // Construct the AuthUser object
      const authUser: AuthUser = {
        id: decoded.sub,
        email: email,
        role: role,
        tenantId: tenantId,
        metadata: userFromSession.user_metadata ?? {},
      };
      
      logSecurityEvent(supabase, 'auth.user_created_debug', { 
        message: 'AuthUser created from session', 
        userId: authUser.id, 
        role: authUser.role, 
        tenantId: authUser.tenantId 
      });
      
      return authUser;
    } else {
      logSecurityEvent(supabase, 'auth.jwt_parse_failed', { 
        message: 'Failed to parse JWT payload in createAuthUserFromSession.' 
      });
      return null;
    }
  } catch (error: any) {
    logSecurityEvent(supabase, 'auth.session_processing_error', { 
      message: 'Error processing session/JWT in createAuthUserFromSession', 
      error: error.message 
    });
    return null;
  }
}

/**
 * Wraps a route handler with authentication and typed database access
 * This is the main authentication wrapper for API routes
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withAuth(handler: AuthRouteHandler) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a Supabase client for the API route
      const supabase = createServerClientForUser();

      // Get the current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        console.error('Authentication error:', userError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Get the user's session for JWT claims
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();

      if (sessionError || !session) {
        console.error('Session error:', sessionError);
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Create AuthUser from session
      const authUser = createAuthUserFromSession(supabase, session);

      if (!authUser) {
        console.error('Failed to create AuthUser from session');
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }

      // Enhance the Supabase client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Call the handler with the authenticated user and enhanced client
      return await handler(req, authUser, enhancedClient, context);
    } catch (error) {
      console.error('Error in route handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Wraps a route handler with service role access
 * Bypasses authentication - use only for admin/system operations
 *
 * @param handler The route handler function
 * @returns A Next.js API route handler
 */
export function withServiceRole(
  handler: (
    req: NextRequest,
    supabase: SupabaseClient<Database>,
    context: Record<string, any>
  ) => Promise<Response>
) {
  return async (
    req: NextRequest,
    context: { params: Record<string, string> }
  ): Promise<Response> => {
    try {
      // Create a service client
      const supabase = createServiceClient();

      // Enhance the Supabase client with schema support
      const enhancedClient = enhanceClientWithSchemas(supabase);

      // Call the handler with the service client
      return await handler(req, enhancedClient, context);
    } catch (error) {
      console.error('Error in service role handler:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}

// Helper function to check if a role is valid (imported from types)
function isValidUserRole(role: unknown): role is UserRole {
  return typeof role === 'string' &&
    ['authenticated', 'superadmin', 'partner', 'attorney', 'paralegal', 'staff', 'client', 'admin'].includes(role as string);
}
