/**
 * Permissions and Role Management
 * Handles role checking, permissions, and super admin access
 */

import { <PERSON>th<PERSON><PERSON>, UserRole, SUPER_ADMIN_EMAILS, SuperAdminEmail } from './types';
import { getUnifiedSession, getUserRoles, type UnifiedSession } from './getUnifiedSession';

/**
 * Checks if a user has one of the specified roles
 *
 * @param user The user to check
 * @param roles Array of roles to check against
 * @returns True if the user has one of the specified roles
 */
export function hasRole(user: AuthUser | null, roles: UserRole[]): boolean {
  if (!user) return false;

  // Check primary role property
  if (user.role && roles.includes(user.role)) {
    return true;
  }

  // Check metadata.roles array if it exists (legacy support)
  if (user.metadata?.roles && Array.isArray(user.metadata.roles)) {
    return user.metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  return false;
}

/**
 * Checks if a user is a super admin
 * Super admins have platform-level access across all tenants
 *
 * @param user The user to check
 * @returns True if the user is a super admin
 */
export function isSuperAdmin(user: AuthUser | null): boolean {
  if (!user) return false;

  // Check if user has superadmin role
  if (user.role === UserRole.Superadmin) {
    return true;
  }

  // Check if user email is in the super admin list
  if (user.email && SUPER_ADMIN_EMAILS.includes(user.email as SuperAdminEmail)) {
    return true;
  }

  // Check metadata for superadmin role (legacy support)
  if (user.metadata?.roles && Array.isArray(user.metadata.roles)) {
    return user.metadata.roles.includes('superadmin');
  }

  return false;
}

/**
 * Checks if a user is authenticated (has a valid user object)
 *
 * @param user The user to check
 * @returns True if the user is authenticated
 */
export function isAuthenticated(user: AuthUser | null): boolean {
  return !!user && !!user.id;
}

/**
 * Legacy function to check if a Supabase user is authenticated
 * Used for backward compatibility with existing code
 *
 * @param user The Supabase user to check
 * @returns True if the user is authenticated
 */
export function isAuthenticatedLegacy(user: any): boolean {
  return !!user && !!user.id;
}

/**
 * Checks if a user has permission to access a specific resource
 * This is a more granular permission check beyond role-based access
 *
 * @param user The user to check
 * @param permission The permission to check
 * @param resourceType Optional resource type for context
 * @param resourceId Optional resource ID for context
 * @returns True if the user has the permission
 */
export function checkPermission(
  user: AuthUser | null,
  permission: string,
  resourceType?: string,
  resourceId?: string
): boolean {
  if (!user) return false;

  // Super admins have all permissions
  if (isSuperAdmin(user)) {
    return true;
  }

  // Role-based permission mapping
  const rolePermissions: Record<UserRole, string[]> = {
    [UserRole.Superadmin]: ['*'], // All permissions
    [UserRole.Partner]: [
      'read:cases',
      'write:cases',
      'delete:cases',
      'read:clients',
      'write:clients',
      'delete:clients',
      'read:documents',
      'write:documents',
      'delete:documents',
      'read:users',
      'write:users',
      'admin:tenant'
    ],
    [UserRole.Attorney]: [
      'read:cases',
      'write:cases',
      'read:clients',
      'write:clients',
      'read:documents',
      'write:documents'
    ],
    [UserRole.Paralegal]: [
      'read:cases',
      'write:cases',
      'read:clients',
      'read:documents',
      'write:documents'
    ],
    [UserRole.Staff]: [
      'read:cases',
      'read:clients',
      'read:documents'
    ],
    [UserRole.Client]: [
      'read:own_case',
      'read:own_documents'
    ],
    [UserRole.Admin]: [
      'read:cases',
      'write:cases',
      'read:clients',
      'write:clients',
      'read:documents',
      'write:documents',
      'admin:tenant'
    ],
    [UserRole.Authenticated]: [
      'read:profile'
    ]
  };

  const userPermissions = rolePermissions[user.role] || [];

  // Check for wildcard permission
  if (userPermissions.includes('*')) {
    return true;
  }

  // Check for exact permission match
  if (userPermissions.includes(permission)) {
    return true;
  }

  // Handle resource-specific permissions
  if (resourceType && resourceId) {
    // For client role, check if they're accessing their own resources
    if (user.role === UserRole.Client) {
      if (permission === 'read:own_case' || permission === 'read:own_documents') {
        // This would need additional logic to verify ownership
        // For now, we'll allow if the user is authenticated
        return true;
      }
    }
  }

  return false;
}

/**
 * Gets all permissions for a user based on their role
 *
 * @param user The user to get permissions for
 * @returns Array of permissions the user has
 */
export function getUserPermissions(user: AuthUser | null): string[] {
  if (!user) return [];

  if (isSuperAdmin(user)) {
    return ['*']; // All permissions
  }

  const rolePermissions: Record<UserRole, string[]> = {
    [UserRole.Superadmin]: ['*'],
    [UserRole.Partner]: [
      'read:cases',
      'write:cases',
      'delete:cases',
      'read:clients',
      'write:clients',
      'delete:clients',
      'read:documents',
      'write:documents',
      'delete:documents',
      'read:users',
      'write:users',
      'admin:tenant'
    ],
    [UserRole.Attorney]: [
      'read:cases',
      'write:cases',
      'read:clients',
      'write:clients',
      'read:documents',
      'write:documents'
    ],
    [UserRole.Paralegal]: [
      'read:cases',
      'write:cases',
      'read:clients',
      'read:documents',
      'write:documents'
    ],
    [UserRole.Staff]: [
      'read:cases',
      'read:clients',
      'read:documents'
    ],
    [UserRole.Client]: [
      'read:own_case',
      'read:own_documents'
    ],
    [UserRole.Admin]: [
      'read:cases',
      'write:cases',
      'read:clients',
      'write:clients',
      'read:documents',
      'write:documents',
      'admin:tenant'
    ],
    [UserRole.Authenticated]: [
      'read:profile'
    ]
  };

  return rolePermissions[user.role] || [];
}

/**
 * Checks if a user can access a specific tenant
 * Ensures proper tenant isolation
 *
 * @param user The user to check
 * @param tenantId The tenant ID to check access for
 * @returns True if the user can access the tenant
 */
export function canAccessTenant(user: AuthUser | null, tenantId: string): boolean {
  if (!user) return false;

  // Super admins can access any tenant
  if (isSuperAdmin(user)) {
    return true;
  }

  // Users can only access their own tenant
  return user.tenantId === tenantId;
}

/**
 * Check if current session has specific roles using unified session
 * This is the new preferred method that works with both Supabase and legacy auth
 *
 * @param roles Array of roles to check against
 * @returns True if the current session has one of the specified roles
 */
export async function hasRoleUnified(roles: (UserRole | string)[]): Promise<boolean> {
  try {
    const session = await getUnifiedSession();
    if (!session) return false;

    const userRoles = getUserRoles(session);
    return userRoles.some(role => roles.includes(role));
  } catch (error) {
    console.error('Error checking unified role:', error);
    return false;
  }
}

/**
 * Legacy function to check if a Supabase user has specific roles
 * Used for backward compatibility with existing code
 *
 * @param user The Supabase user to check
 * @param roles Array of roles to check against
 * @returns True if the user has one of the specified roles
 */
export function hasRoleLegacy(user: any, roles: (UserRole | string)[]): boolean {
  if (!user) return false;

  // Check user_metadata for role information
  const userRole = user.role || user.user_metadata?.role;
  if (userRole && roles.includes(userRole)) {
    return true;
  }

  // Check metadata.roles array if it exists
  if (user.user_metadata?.roles && Array.isArray(user.user_metadata.roles)) {
    return user.user_metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  // Check app_metadata.roles array if it exists (Supabase standard)
  if (user.app_metadata?.roles && Array.isArray(user.app_metadata.roles)) {
    return user.app_metadata.roles.some((role: string) =>
      roles.includes(role as UserRole)
    );
  }

  return false;
}
