import { withAuth, AuthUser } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

// GET /api/tasks/[id]/history - Get history for a specific task
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const params = context.params as { id: string };
    const taskId = params.id;

    // Get the task history with proper schema specification
    const { data, error } = await (supabase as any)
      .schema('tenants')
      .from('task_history')
      .select('*')
      .eq('task_id', taskId)
      .eq('tenant_id', user.tenantId || '')
      .order('changed_at', { ascending: false });

    if (error) {
      console.error('Error fetching task history:', error);
      return NextResponse.json({ error: 'Failed to fetch task history' }, { status: 500 });
    }

    return NextResponse.json({ history: data });
  } catch (error) {
    console.error('Error in GET /api/tasks/[id]/history:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});
