// frontend/src/app/api/tasks/route.ts
import { withAuth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';
import { z } from 'zod';
import type { Database } from '@/lib/supabase/database.types';
import { createDataAccess } from '@/lib/data-access';

// Zod schema for task validation
const TaskSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  due_date: z.string().optional(),
  status: z.enum(['pending', 'in_progress', 'completed', 'cancelled']).default('pending'),
  assigned_to: z.string().uuid().optional(),
  related_case: z.string().uuid().optional(),
});

// GET /api/tasks - Get all tasks for current tenant using the data access layer
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Log the user object and tenantId received from withAuth
    console.log('[GET /api/tasks] User received:', JSON.stringify(user, null, 2));
    console.log('[GET /api/tasks] Tenant ID:', user?.tenantId);

    // Add a check for missing tenantId
    if (!user?.tenantId) {
      console.error('[GET /api/tasks] Error: Tenant ID is missing from authenticated user.');
      return NextResponse.json(
        { error: 'Internal server error', details: 'Tenant ID missing from user context.' },
        { status: 500 }
      );
    }

    // Create a data access layer
    const dataAccess = createDataAccess(supabase, user.tenantId);

    // Extract query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const statusParam = searchParams.get('status') || undefined;
    const assignedTo = searchParams.get('assigned_to') || undefined;
    const relatedCase = searchParams.get('related_case') || undefined;
    const searchTerm = searchParams.get('search') || undefined;

    // Validate status parameter using Zod safeParse
    const statusSchema = TaskSchema.shape.status; // Extract the status schema part
    const statusParseResult = statusParam ? statusSchema.safeParse(statusParam) : undefined;
    const validatedStatus = statusParseResult?.success ? statusParseResult.data : undefined;

    // Get tasks using the tasks repository
    const result = await dataAccess.tasks.getTasks({
      page,
      limit,
      status: validatedStatus,
      assigned_to: assignedTo,
      case_id: relatedCase, // Use case_id instead of related_case
      searchTerm
    });

    return NextResponse.json({
      tasks: result.data || [],
      total: result.count || 0,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/tasks:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// POST /api/tasks - Create a new task using the data access layer
export const POST = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Log the user object and tenantId received from withAuth
    console.log('[POST /api/tasks] User received:', JSON.stringify(user, null, 2));
    console.log('[POST /api/tasks] Tenant ID:', user?.tenantId);

    // Add a check for missing tenantId
    if (!user?.tenantId) {
      console.error('[POST /api/tasks] Error: Tenant ID is missing from authenticated user.');
      return NextResponse.json(
        { error: 'Internal server error', details: 'Tenant ID missing from user context.' },
        { status: 500 }
      );
    }

    // Parse and validate request body
    const body = await req.json();
    const validationResult = TaskSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const validatedData = validationResult.data;

    // Create a data access layer
    const dataAccess = createDataAccess(supabase, user.tenantId);

    // Create a new task using the tasks repository
    const task = await dataAccess.tasks.create({
      ...validatedData,
      tenant_id: user.tenantId, // Add tenant_id
      created_by: user.id
    });

    return NextResponse.json(task, { status: 201 });
  } catch (error: unknown) {
    console.error('Error in POST /api/tasks:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});

// Note: PUT and DELETE operations are now handled in /app/api/tasks/[id]/route.ts
