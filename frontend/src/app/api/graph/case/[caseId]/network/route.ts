import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON><PERSON>, AuthUser, UserR<PERSON> } from '@/lib/auth';
import { graphService } from '@/lib/services/graph-service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

// Define the GraphData interface
interface GraphData {
  nodes: Array<{
    id: string;
    name: string;
    type: string;
    [key: string]: unknown;
  }>;
  links: Array<{
    source: string;
    target: string;
    type: string;
    [key: string]: unknown;
  }>;
}

// Define roles who can access this endpoint
const ALLOWED_ROLES: UserRole[] = [UserRole.Partner, UserRole.Attorney, UserRole.Paralegal, UserRole.Staff]; // Use enum members

export const GET = withAuth(
  async (
    req: NextRequest,
    user: AuthUser, // User object from withAuth
    supabase: SupabaseClient<Database, "public", any>, // Supabase client provided by withAuth
    context: Record<string, unknown> // Context object from withAuth
  ): Promise<Response> => {
    // Extract params from context
    const params = context.params as { caseId?: string } | undefined;

    // Refined check for optional params and specific caseId
    const caseId = params?.caseId;
    if (!caseId) {
      return NextResponse.json({ error: 'Case ID parameter is missing or invalid' }, { status: 400 });
    }

    const tenantId = user.tenantId;

    if (!tenantId) {
        return NextResponse.json({ error: 'Tenant ID not found in user session' }, { status: 400 });
    }

    // Optional: Add explicit role check if needed beyond withAuth's checks
    // if (!ALLOWED_ROLES.includes(user.role)) {
    //   return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    // }

    try {
      console.log(`API Route: Fetching network for case ${caseId} in tenant ${tenantId}`);
      const graphData = await graphService.getCaseNetwork(caseId, tenantId);
      console.log(`API Route: Found ${graphData.nodes.length} nodes and ${graphData.links.length} links for case ${caseId}`);
      return NextResponse.json(graphData);
    } catch (error: unknown) {
      console.error(`API Error fetching case network for case ${caseId}:`, error);
      return NextResponse.json({ error: 'Failed to retrieve case network.' }, { status: 500 });
    }
  }
);
