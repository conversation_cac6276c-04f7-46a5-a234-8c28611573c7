// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth';
import { graphService } from '@/lib/services/graph-service';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define roles who can access this endpoint
const ALLOWED_ROLES: UserRole[] = ['partner', 'attorney', 'paralegal', 'staff'];

export const POST = withAuth(
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    const tenantId = user.tenantId;
    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID not found in user session' }, { status: 400 });
    }

    let checkInput: any;
    try {
      checkInput = await req.json();
    } catch (error: any) {
      return NextResponse.json({ error: 'Invalid JSON body provided for conflict check.' }, { status: 400 });
    }

    // Basic validation (can be expanded)
    if (!checkInput || typeof checkInput !== 'object') {
        return NextResponse.json({ error: 'Conflict check input is missing or invalid.' }, { status: 400 });
    }

    // TODO: Add more specific validation for checkInput fields (e.g., personName, companyName)

    try {
      console.log(`API Route: Running conflict check for tenant ${tenantId} with input:`, checkInput);
      const conflicts = await graphService.runConflictCheck(checkInput, tenantId);
      console.log(`API Route: Conflict check for tenant ${tenantId} returned ${conflicts.length} conflicts.`);
      return NextResponse.json(conflicts);
    } catch (error: any) {
      console.error(`API Error running conflict check for tenant ${tenantId}:`, error);
      return NextResponse.json({ error: 'Failed to run conflict check.' }, { status: 500 });
    }
  },
  ALLOWED_ROLES
);
