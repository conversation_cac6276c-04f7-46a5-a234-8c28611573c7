// @ts-nocheck - API route type issues
/**
 * Special API endpoint to test direct Supabase REST API calls with proper schema handling
 */
import { NextRequest, NextResponse } from 'next/server';
import { schemaClient, enhanceClientWithSchemas } from '@/lib/supabase/schema-client';
import { withAuth } from '@/lib/auth';
import type { AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';

/**
 * GET handler that uses the schema-aware client to query the tasks table
 */
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Enhance the client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Test 1: Using the schema-aware client that patches the URL
    console.log('Using schema-aware client to query tenants.tasks');
    const { data: schemaData, error: schemaError } = await enhancedClient.tenants
      .from('tasks')
      .select('*')
      .eq('tenant_id', user.tenantId);

    if (schemaError) {
      console.error('Schema client error:', schemaError);
    }

    // Test 2: Using the schema method explicitly
    console.log('Using explicit schema method');
    const { data: explicitData, error: explicitError } = await supabase
      .schema('tenants')
      .from('tasks')
      .select('*')
      .eq('tenant_id', user.tenantId) as { data: any[] | null, error: any };

    if (explicitError) {
      console.error('Explicit schema error:', explicitError);
    }

    // Test 3: Using the schema method with variables
    console.log('Using schema method with variables');
    // Create a custom schema for tasks
    const customSchema = {
      name: 'tasks',
      schema: 'tenants'
    };

    const { data: qualifiedData, error: qualifiedError } = await supabase
      .schema(customSchema.schema as any)
      .from(customSchema.name as any)
      .select('*')
      .eq('tenant_id', user.tenantId);

    if (qualifiedError) {
      console.error('Schema method with variables error:', qualifiedError);
    }

    // Return results from all approaches for comparison
    return NextResponse.json({
      schemaClient: {
        success: !schemaError,
        count: schemaData?.length || 0,
        error: schemaError ? schemaError.message : null
      },
      explicitSchema: {
        success: !explicitError,
        count: explicitData?.length || 0,
        error: explicitError ? explicitError.message : null
      },
      schemaWithVariables: {
        success: !qualifiedError,
        count: qualifiedData?.length || 0,
        error: qualifiedError ? qualifiedError.message : null
      }
    });
  } catch (error: any) {
    console.error('Error in tasks-schema-test:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: String(error),
      stack: error.stack
    }, { status: 500 });
  }
});
