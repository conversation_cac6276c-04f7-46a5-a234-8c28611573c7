// @ts-nocheck - API route type issues
/**
 * Special API endpoint to test direct Supabase REST API calls with proper schema handling for clients
 */
import { NextRequest, NextResponse } from 'next/server';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';
import { withAuth } from '@/lib/auth';
import type { AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';

/**
 * GET handler that uses the schema-aware client to query the clients table
 */
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Enhance the client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Test 1: Using the schema-aware client that patches the URL
    console.log('Using schema-aware client to query tenants.clients');
    console.log('Tenant ID:', user.tenantId);
    const { data: schemaData, error: schemaError } = await enhancedClient
      .schema('tenants')
      .from('clients')
      .select('*')
      .eq('tenant_id', user.tenantId)
      .limit(10);

    if (schemaError) {
      console.error('Schema client error:', schemaError);
      return NextResponse.json({
        success: false,
        message: 'Schema client error',
        error: schemaError.message,
        method: 'schema-client'
      }, { status: 500 });
    }

    // Test 2: Using the schema method directly
    console.log('Using schema method directly to query tenants.clients');
    const { data: directData, error: directError } = await supabase
      .schema('tenants')
      .from('clients')
      .select('*')
      .eq('tenant_id', user.tenantId)
      .limit(10);

    if (directError) {
      console.error('Schema method direct error:', directError);
      return NextResponse.json({
        success: false,
        message: 'Schema method direct error',
        error: directError.message,
        method: 'schema-method-direct'
      }, { status: 500 });
    }

    // Test 3: Using the schema method
    console.log('Using schema method to query tenants.clients');
    const { data: schemaMethodData, error: schemaMethodError } = await supabase
      .schema('tenants')
      .from('clients')
      .select('*')
      .eq('tenant_id', user.tenantId)
      .limit(10);

    if (schemaMethodError) {
      console.error('Schema method error:', schemaMethodError);
      return NextResponse.json({
        success: false,
        message: 'Schema method error',
        error: schemaMethodError.message,
        method: 'schema-method'
      }, { status: 500 });
    }

    // Return the results of all tests
    return NextResponse.json({
      success: true,
      message: 'All tests passed',
      data: {
        schemaClient: schemaData || [],
        directReference: directData || [],
        schemaMethod: schemaMethodData || []
      },
      count: {
        schemaClient: schemaData?.length || 0,
        directReference: directData?.length || 0,
        schemaMethod: schemaMethodData?.length || 0
      }
    });
  } catch (err) {
    console.error('Error in clients-test route:', err instanceof Error ? err.message : String(err));
    console.error('Error stack:', err instanceof Error ? err.stack : 'No stack trace available');
    return NextResponse.json({
      success: false,
      message: 'Error in clients-test route',
      error: err instanceof Error ? err.message : String(err)
    }, { status: 500 });
  }
}, ['partner', 'attorney', 'paralegal', 'staff']);
