// @ts-check - API route type issues
import { withAuth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';

// Define interfaces for user data
interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role: string;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
  tenant_id: string;
  full_name?: string;
  firms?: {
    id: string;
    name: string;
    status: string;
  };
}

// Define specific type for clarity
type TenantUserRow = Database['tenants']['Tables']['users']['Row'];

// Define the shape of the firms data selected in the query
type SelectedFirmData = {
  id: string;
  name: string;
  status: string | null; // Assuming status can be null based on schema
};

// Type for the data fetched from Supabase, potentially including firms
type UserData = TenantUserRow & { firms?: SelectedFirmData | null };

// Interface for the final processed user data sent in the response
interface ProcessedUser extends Omit<UserData, 'created_at' | 'updated_at' | 'firms'> {
  full_name: string;
  created_at: string | null; // Ensure these are strings for JSON
  updated_at: string | null;
  firms?: SelectedFirmData | null;
}

// GET /api/users - Get all users for current tenant
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // For admin/partner users, we might want to include tenant information
    const isAdmin = user.role === 'partner' || user.role === 'attorney';

    // Base select query for user fields
    let selectQuery = `
      id,
      email,
      first_name,
      last_name,
      role,
      avatar_url,
      created_at,
      updated_at,
      tenant_id
    `;

    // Conditionally add the firms relation for admin users
    if (isAdmin) {
      selectQuery += ', firms!tenant_id(id, name, status)';
    }

    // Fetch data using the constructed query and explicit type
    const { data, error } = await (supabase as any)
      .schema('tenants')
      .from('users')
      .select(selectQuery) // Remove type arguments since supabase is cast as any
      .eq('tenant_id', user.tenantId || '')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching users:', error);
      return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
    }

    // Process the data using the correctly typed UserData
    const processedUsers: ProcessedUser[] = data.map((tenantUser: UserData) => ({
      ...tenantUser,
      full_name: `${tenantUser.first_name || ''} ${tenantUser.last_name || ''}`.trim() || 'Unnamed User',
      // Convert dates to ISO strings for JSON compatibility
      created_at: tenantUser.created_at ? new Date(tenantUser.created_at).toISOString() : null,
      updated_at: tenantUser.updated_at ? new Date(tenantUser.updated_at).toISOString() : null,
      // firms property is already included via ...tenantUser spread and correctly typed
    }));

    return NextResponse.json({ users: processedUsers });
  } catch (error: unknown) {
    console.error('Error in GET /api/users:', error);
    let errorMessage = 'Internal server error';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
});
