// frontend/src/app/api/deadlines/validation/stats/route.ts
import { withAuth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';

// GET /api/deadlines/validation/stats - Get validation statistics
export const GET = withAuth(async (
  _req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient
) => {
  try {
    // Directly fetch counts for each validation status
    const pendingResult = await supabase
      .from('tenants.deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'pending');

    const validatedResult = await supabase
      .from('tenants.deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'validated');

    const rejectedResult = await supabase
      .from('tenants.deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'rejected');

    // Check for errors in any of the queries
    if (pendingResult.error || validatedResult.error || rejectedResult.error) {
      const error = pendingResult.error || validatedResult.error || rejectedResult.error;
      console.error('Error fetching validation stats:', error);
      return NextResponse.json({ error: 'Failed to fetch validation statistics' }, { status: 500 });
    }

    // Calculate total count
    const pendingCount = pendingResult.count || 0;
    const validatedCount = validatedResult.count || 0;
    const rejectedCount = rejectedResult.count || 0;
    const totalCount = pendingCount + validatedCount + rejectedCount;

    // Calculate percentages
    const pendingPercentage = totalCount > 0 ? Math.round((pendingCount / totalCount) * 100) : 0;
    const validatedPercentage = totalCount > 0 ? Math.round((validatedCount / totalCount) * 100) : 0;
    const rejectedPercentage = totalCount > 0 ? Math.round((rejectedCount / totalCount) * 100) : 0;

    // Return the response object
    return NextResponse.json({
      counts: {
        pending: pendingCount,
        validated: validatedCount,
        rejected: rejectedCount
      },
      percentages: {
        pending: pendingPercentage,
        validated: validatedPercentage,
        rejected: rejectedPercentage
      },
      total: totalCount
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/deadlines/validation/stats:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
});
