// frontend/src/app/api/deadlines/validation/[id]/route.ts
import { withAuth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth';
import { TypedSupabaseClient } from '@/lib/supabase/client-types';
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/supabase/database.types';
import { z } from 'zod';
import { createServices } from '@/lib/services';
import type { Deadline } from '@/types/domain/tenants/Deadline';

// Validation request schema
const ValidationRequestSchema = z.object({
  action: z.enum(['validate', 'reject']),
  note: z.string().optional()
});

// POST /api/deadlines/validation/[id] - Validate or reject a deadline
export const POST = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  // Extract params from context safely
  const params = context.params;
  if (typeof params !== 'object' || params === null || !('id' in params) || typeof params.id !== 'string') {
    return NextResponse.json({ error: 'Invalid deadline ID in URL' }, { status: 400 });
  }
  const deadlineId = params.id;

  try {
    // Ensure tenantId exists before proceeding
    if (!user.tenantId) {
      console.error('User does not have a tenantId');
      return NextResponse.json({ error: 'User tenant association missing.' }, { status: 400 });
    }

    // Validate deadlineId format
    if (!z.string().uuid().safeParse(deadlineId).success) {
      return NextResponse.json({ error: 'Invalid deadline ID format' }, { status: 400 });
    }

    // Parse and validate request body
    const body: unknown = await req.json();
    const validationResult = ValidationRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json({
        error: 'Validation failed',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const { action, note } = validationResult.data;

    // Create services with tenant context - this ensures proper tenant isolation
    const services = createServices(supabase as TypedSupabaseClient, user.tenantId || '');

    try {
      // Use the service to get the deadline
      const deadline = await services.deadlines.getById(deadlineId);

      if (!deadline) {
        return NextResponse.json({ error: 'Deadline not found' }, { status: 404 });
      }

      // Update the validation status using the service
      const updatedDeadline = await services.deadlines.updateValidationStatus(
        deadlineId,
        user.id,
        action,
        note
      );

      return NextResponse.json(updatedDeadline);
    } catch (serviceError) {
      // Service errors are already logged and have detailed messages
      console.error('Service error:', serviceError);
      if (serviceError instanceof Error) {
        return NextResponse.json({ error: serviceError.message }, { status: 500 });
      }
      return NextResponse.json({ error: 'Unknown service error' }, { status: 500 });
    }
  } catch (error: unknown) {
    console.error('Error in POST /api/deadlines/validation/[id]:', error);
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation failed', details: error.errors }, { status: 400 });
    }
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
