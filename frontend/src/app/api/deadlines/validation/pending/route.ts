// frontend/src/app/api/deadlines/validation/pending/route.ts
import { withAuth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';

// GET /api/deadlines/validation/pending - Get all pending deadlines for validation
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient
) => {
  try {
    // Parse query parameters
    const url = new URL(req.url);
    const page = url.searchParams.get('page') ? parseInt(url.searchParams.get('page') as string) : 1;
    const limit = url.searchParams.get('limit') ? parseInt(url.searchParams.get('limit') as string) : 50;

    // Query for pending validation deadlines
    const { data: pendingDeadlines, error, count } = await supabase
      .schema('tenants')
      .from('deadlines')
      .select('*, documents(title), cases(title)', { count: 'exact' })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'pending')
      .order('date', { ascending: true })
      .range((page - 1) * limit, page * limit - 1);

    if (error) {
      console.error('Error fetching pending deadlines:', error);
      return NextResponse.json({ error: 'Failed to fetch pending deadlines' }, { status: 500 });
    }

    // Get validation statistics - query each status individually
    const pendingStatsResult = await supabase
      .from('tenants.deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'pending');

    const validatedStatsResult = await supabase
      .from('tenants.deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'validated');

    const rejectedStatsResult = await supabase
      .from('tenants.deadlines')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', user.tenantId)
      .eq('validation_status', 'rejected');

    const statsError = pendingStatsResult.error || validatedStatsResult.error || rejectedStatsResult.error;

    if (statsError) {
      console.error('Error fetching validation stats:', statsError);
      return NextResponse.json({
        deadlines: pendingDeadlines,
        pagination: {
          page,
          limit,
          totalCount: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      });
    }

    // Calculate total count
    const pendingCount = pendingStatsResult.count || 0;
    const validatedCount = validatedStatsResult.count || 0;
    const rejectedCount = rejectedStatsResult.count || 0;
    const totalCount = pendingCount + validatedCount + rejectedCount;

    // Calculate percentages
    const pendingPercentage = totalCount > 0 ? Math.round((pendingCount / totalCount) * 100) : 0;
    const validatedPercentage = totalCount > 0 ? Math.round((validatedCount / totalCount) * 100) : 0;
    const rejectedPercentage = totalCount > 0 ? Math.round((rejectedCount / totalCount) * 100) : 0;

    // Create validation stats object
    const validationStats = {
      counts: {
        pending: pendingCount,
        validated: validatedCount,
        rejected: rejectedCount
      },
      percentages: {
        pending: pendingPercentage,
        validated: validatedPercentage,
        rejected: rejectedPercentage
      },
      total: totalCount
    };

    return NextResponse.json({
      deadlines: pendingDeadlines,
      stats: validationStats,
      pagination: {
        page,
        limit,
        totalCount: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });
  } catch (error: unknown) {
    console.error('Error in GET /api/deadlines/validation/pending:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
