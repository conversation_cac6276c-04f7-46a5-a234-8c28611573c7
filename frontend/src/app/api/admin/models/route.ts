import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server-new';
import { isAuthenticatedLegacy as isAuthenticated, hasRoleLegacy as hasRole, UserRole } from '@/lib/auth';
import { logSecurityEvent } from '@/lib/security/forensics';

// Define model provider types
export type ModelProvider = 'openai' | 'anthropic' | 'cohere' | 'mistral' | 'google';

// Define model interface
export interface LLMModel {
  id: string;
  provider: ModelProvider;
  name: string;
  description?: string;
  maxTokens: number;
  isDefault: boolean;
}

/**
 * GET /api/admin/models
 * Get all available models and current default model
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const provider = searchParams.get('provider') as ModelProvider | null;

    // Get models from the database
    const { data: models, error: modelsError } = provider
      ? await (supabase as any)
          .schema('security')
          .from('llm_models')
          .select('*')
          .eq('provider', provider)
      : await (supabase as any)
          .schema('security')
          .from('llm_models')
          .select('*');

    if (modelsError) {
      console.error('Error fetching models:', modelsError);
      return NextResponse.json({ error: 'Failed to fetch models' }, { status: 500 });
    }

    // Get default models
    const { data: defaultModels, error: defaultModelsError } = await (supabase as any)
      .schema('security')
      .from('llm_default_models')
      .select('*');

    if (defaultModelsError) {
      console.error('Error fetching default models:', defaultModelsError);
      return NextResponse.json({ error: 'Failed to fetch default models' }, { status: 500 });
    }

    // Process models to include default flag
    const processedModels = (models || []).map((model: any) => {
      const isDefault = (defaultModels || []).some(
        (defaultModel: any) =>
          defaultModel.provider === model.provider &&
          defaultModel.model_id === model.id
      );

      return {
        ...model,
        isDefault
      };
    });

    return NextResponse.json({
      models: processedModels,
      defaultModels: (defaultModels || []).reduce((acc: Record<string, string>, curr: any) => {
        acc[curr.provider] = curr.model_id;
        return acc;
      }, {} as Record<string, string>)
    });
  } catch (error) {
    console.error('Error in models API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/models
 * Set the default model for a provider
 */
export async function PATCH(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, [UserRole.Superadmin])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { provider, modelId } = body;

    // Validate required fields
    if (!provider || !modelId) {
      return NextResponse.json({ error: 'Provider and model ID are required' }, { status: 400 });
    }

    // Check if the model exists
    const { data: model, error: modelError } = await (supabase as any)
      .schema('security')
      .from('llm_models')
      .select('*')
      .eq('provider', provider)
      .eq('id', modelId)
      .single();

    if (modelError) {
      console.error('Error checking model:', modelError);
      return NextResponse.json({ error: 'Model not found' }, { status: 404 });
    }

    // Update the default model
    const { data, error } = await (supabase as any)
      .schema('security')
      .from('llm_default_models')
      .upsert({
        provider,
        model_id: modelId,
        updated_at: new Date().toISOString(),
        updated_by: user.email || user.id
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating default model:', error);
      return NextResponse.json({ error: 'Failed to update default model' }, { status: 500 });
    }

    // Log the security event
    await logSecurityEvent(supabase, 'model.default_updated', {
      provider,
      modelId,
      userId: user.id,
      userEmail: user.email
    });

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Error in models API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
