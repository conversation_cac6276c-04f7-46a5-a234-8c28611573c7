// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { Database } from '@/lib/supabase/database.types';
import { createClient, isAuthenticatedLegacy as isAuthenticated, hasRole } from '@/lib/auth';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

/**
 * GET /api/admin/voice-agent
 * Get voice agent configurations
 */
export async function GET(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Create an enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);
    // Access the security schema
    const securityClient = enhancedClient.security;

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const tenantId = searchParams.get('tenantId');
    const agentType = searchParams.get('agentType');

    if (!tenantId) {
      return NextResponse.json({ error: 'Tenant ID is required' }, { status: 400 });
    }

    // Build query
    let query = securityClient
      .from('voice_agent_configs')
      .select('*')
      .eq('tenant_id', tenantId);

    if (agentType) {
      query = query.eq('agent_type', agentType);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching voice agent configs:', error);
      return NextResponse.json({ error: 'Failed to fetch voice agent configs' }, { status: 500 });
    }

    // Mask API keys for security
    const maskedData = data.map((config: any) => ({
      ...config,
      api_key: config.api_key ? '••••••••' + config.api_key.slice(-4) : null
    }));

    return NextResponse.json({ configs: maskedData });
  } catch (error) {
    console.error('Error in voice agent API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * POST /api/admin/voice-agent
 * Manage voice agent configurations
 */
export async function POST(request: NextRequest) {
  try {
    // Create a Supabase client
    const supabase = createClient();
    // Create an enhanced client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);
    // Access the security schema
    const securityClient = enhancedClient.security;

    // Get the user from the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError || !session) {
      console.error('Session error:', sessionError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;

    // Check if user is authenticated
    if (!isAuthenticated(user)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is a superadmin
    if (!hasRole(user, ['superadmin'])) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }
    const body = await request.json();

    // Determine the action to take
    const { action, data } = body;

    if (action === 'generateApiKey') {
      // Generate a new API key
      const { data: apiKey, error } = await supabase
        .rpc('generate_voice_agent_api_key' as any, {
          p_tenant_id: data.tenantId,
          p_agent_type: data.agentType
        });

      if (error) {
        console.error('Error generating API key:', error);
        return NextResponse.json({ error: 'Failed to generate API key' }, { status: 500 });
      }

      return NextResponse.json({ apiKey });
    }
    else if (action === 'updateConfig') {
      // Update voice agent configuration
      const now = new Date();

      // Check if config exists
      const { data: existingConfig, error: findError } = await securityClient
        .from('voice_agent_configs')
        .select('id')
        .eq('tenant_id', data.tenantId)
        .eq('agent_type', data.agentType)
        .maybeSingle();

      if (findError) {
        console.error('Error checking existing config:', findError);
        return NextResponse.json({ error: 'Failed to check existing config' }, { status: 500 });
      }

      let result;

      if (existingConfig) {
        // Update existing config
        const { data: config, error } = await securityClient
          .from('voice_agent_configs')
          .update({
            is_enabled: data.isEnabled,
            webhook_url: data.webhookUrl,
            config_data: data.configData || {},
            updated_at: now.toISOString()
          })
          .eq('id', existingConfig.id)
          .select()
          .single();

        if (error) {
          console.error('Error updating voice agent config:', error);
          return NextResponse.json({ error: 'Failed to update voice agent config' }, { status: 500 });
        }

        result = { config, updated: true };
      } else {
        // Create new config
        const { data: config, error } = await securityClient
          .from('voice_agent_configs')
          .insert({
            tenant_id: data.tenantId,
            agent_type: data.agentType,
            is_enabled: data.isEnabled,
            webhook_url: data.webhookUrl,
            config_data: data.configData || {},
            created_at: now.toISOString(),
            updated_at: now.toISOString()
          })
          .select()
          .single();

        if (error) {
          console.error('Error creating voice agent config:', error);
          return NextResponse.json({ error: 'Failed to create voice agent config' }, { status: 500 });
        }

        result = { config, created: true };
      }

      // Mask API key for security
      if (result.config.api_key) {
        result.config.api_key = '••••••••' + result.config.api_key.slice(-4);
      }

      return NextResponse.json(result);
    }
    else if (action === 'validateApiKey') {
      // Validate an API key
      const { data: validation, error } = await supabase
        .rpc('validate_voice_agent_api_key' as any, {
          p_api_key: data.apiKey
        });

      if (error) {
        console.error('Error validating API key:', error);
        return NextResponse.json({ error: 'Failed to validate API key' }, { status: 500 });
      }

      return NextResponse.json({ validation });
    }
    else if (action === 'recordUsage') {
      // Record voice agent usage
      const { data: usage, error } = await supabase
        .rpc('record_voice_agent_usage' as any, {
          p_api_key: data.apiKey,
          p_duration_seconds: data.durationSeconds,
          p_call_data: data.callData || null
        });

      if (error) {
        console.error('Error recording voice agent usage:', error);
        return NextResponse.json({ error: 'Failed to record voice agent usage' }, { status: 500 });
      }

      return NextResponse.json({ usage });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Error in voice agent API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
