// @ts-nocheck - API route type issues
import { NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth';

/**
 * Simple endpoint to check authentication status and user role
 * Protected by withAuth wrapper from auth-helpers.ts
 */
export const GET = withAuth(
  async (req, user, _supabase) => {
    // User is already authenticated by withAuth
    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId
      }
    });
  },
  // Allow all authenticated users to access this endpoint
  ['partner', 'attorney', 'paralegal', 'staff', 'client']
);

/**
 * Example of a more restricted endpoint
 */
export const POST = withAuth(
  async (req, user, _supabase) => {
    try {
      const body = await req.json();

      return NextResponse.json({
        message: 'Authentication check successful',
        userRole: user.role,
        receivedData: body
      });
    } catch (error: any) {
      return NextResponse.json(
        { error: 'Failed to process request' },
        { status: 400 }
      );
    }
  },
  // Only staff roles can access this endpoint
  ['partner', 'attorney', 'paralegal', 'staff']
);
