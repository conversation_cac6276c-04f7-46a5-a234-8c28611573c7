// @ts-nocheck - API route type issues
//frontend/src/app/api/test-auth/route.ts
import { NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth';
import { cookies } from 'next/headers';
import { createServerClient } from '@supabase/ssr';

// **GET - Available to multiple roles**
export const GET = withAuth(async (req, user, supabase) => {
  try {
    // Skip database query to avoid errors with missing tables
    // Just return the authenticated user information
    return NextResponse.json({
      message: 'Auth check successful',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId
      },
      testData: null
    });
  } catch (err) {
    console.error('Server error in GET:', err);
    return NextResponse.json({ error: 'Server error', details: String(err) }, { status: 500 });
  }
}, ['partner', 'attorney', 'paralegal', 'staff', 'client']);

// **POST - Restricted to 'partner' role**
export const POST = withAuth(async (req, user, supabase) => {
  try {
    // Skip database operations to avoid errors with missing tables
    // Just return success for testing authentication
    return NextResponse.json({
      message: 'Partner-only action successful',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId
      }
    });
  } catch (err) {
    console.error('Internal error in POST:', err);
    return NextResponse.json({ error: 'Internal server error', details: String(err) }, { status: 500 });
  }
}, ['partner']);

// **PUT - Allowed for staff**
export const PUT = withAuth(async (req, user, supabase) => {
  try {
    // Skip database operations to avoid errors with missing tables
    // Just return success for testing authentication
    return NextResponse.json({
      message: 'Staff update successful',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId
      }
    });
  } catch (err) {
    console.error('Internal error in PUT:', err);
    return NextResponse.json({ error: 'Internal server error', details: String(err) }, { status: 500 });
  }
}, ['partner', 'attorney', 'paralegal', 'staff']);

// **DELETE - Allowed for clients**
export const DELETE = withAuth(async (req, user, supabase) => {
  try {
    // This is a test endpoint, so we'll just return success without actually deleting
    // In a real application, you would implement proper deletion logic

    return NextResponse.json({
      message: 'Client deletion request received (simulated, no actual deletion)',
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId
      }
    });
  } catch (err) {
    console.error('Internal error in DELETE:', err);
    return NextResponse.json({ error: 'Internal server error', details: String(err) }, { status: 500 });
  }
}, ['client']);
