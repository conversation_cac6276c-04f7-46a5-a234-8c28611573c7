// @ts-nocheck - API route type issues
// Special API endpoint to test our schema fix
import { withAuth } from '@/lib/auth';
import { NextRequest, NextResponse } from 'next/server';
import type { AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Database } from '@/lib/supabase/database.types';
import { enhanceClientWithSchemas } from '@/lib/supabase/schema-client';

// GET /api/tasks-fix - Get all tasks for current tenant with schema fix
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Enhance the client with schema support
    const enhancedClient = enhanceClientWithSchemas(supabase);

    // Use the enhanced client to query tasks
    const { data, error, count } = await enhancedClient.tenants
      .from('tasks')
      .select('*, assignee:users!tasks_assigned_to_tenant_user_fkey(id, email, first_name, last_name), case:cases!tasks_related_case_fkey(id, title)', {
        count: 'exact'
      })
      .eq('tenant_id', user.tenantId);

    if (error) {
      // Log the detailed Supabase error object
      console.error('Supabase error fetching tasks:', JSON.stringify(error, null, 2));
      return NextResponse.json({ error: 'Failed to fetch tasks', details: error.message }, { status: 500 });
    }

    return NextResponse.json({
      tasks: data || [],
      total: count || 0,
      message: 'Successfully fetched tasks using schema-qualified table name'
    });
  } catch (error: any) {
    console.error('Error in GET /api/tasks-fix:', error);
    return NextResponse.json({ error: 'Internal server error', details: String(error) }, { status: 500 });
  }
});
