// @ts-check - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth';
import { createServices } from '@/lib/services';

/**
 * GET /api/documents/list - List all documents with optional filtering
 *
 * Query parameters:
 * - caseId: Filter by case ID
 * - clientId: Filter by client ID
 * - type: Filter by document type
 * - status: Filter by processing status
 * - search: Search term for document title/content
 * - page: Page number for pagination
 * - limit: Number of documents per page
 * - sortBy: Field to sort by (default: 'created_at')
 * - sortOrder: 'asc' or 'desc' (default: 'desc')
 */
export const GET = withAuth(async (
  req: NextRequest,
  user,
  supabase
) => {
  try {
    // Ensure tenantId exists before proceeding
    if (!user.tenantId) {
      console.error('User does not have a tenantId');
      return NextResponse.json({ error: 'User tenant association missing.' }, { status: 400 });
    }

    // Extract query parameters
    const { searchParams } = new URL(req.url);
    const caseId = searchParams.get('caseId') || undefined;
    const clientId = searchParams.get('clientId') || undefined;
    const documentType = searchParams.get('type') || undefined;
    const status = searchParams.get('status') || undefined;
    const searchTerm = searchParams.get('search') || undefined;
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') === 'asc' ? 'asc' : 'desc';

    // Create document service
    const services = createServices(supabase as any, user.tenantId);

    // Get documents with filters
    const result = await services.documents.list({
      caseId,
      clientId,
      documentType,
      status,
      searchTerm,
      page,
      limit,
      sortBy,
      sortOrder,
    });

    // Return paginated results
    return NextResponse.json({
      documents: result.data,
      pagination: {
        currentPage: page,
        totalPages: Math.ceil(result.total / limit),
        totalCount: result.total,
        hasMore: page * limit < result.total
      }
    });
  } catch (error: unknown) {
    console.error('Error listing documents:', error);
    let errorMessage = 'Failed to list documents';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}); // Allow all authenticated roles
