// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define interfaces for document types
interface DocumentType {
  id: string;
  label: string;
  description: string;
  custom?: boolean;
}

interface TenantSettings {
  tenant_id: string;
  document_types?: DocumentType[];
}

/**
 * GET /api/documents/types - Get available document types
 */
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    // Define document types - these could be fetched from the database in the future
    // For now, we'll define them statically
    const documentTypes: DocumentType[] = [
      { id: "medical", label: "Medical Records", description: "Patient records, medical reports, and healthcare documentation" },
      { id: "insurance", label: "Insurance Documents", description: "Policies, claims, and correspondence with insurance companies" },
      { id: "police", label: "Police Reports", description: "Accident reports, incident documentation, and law enforcement records" },
      { id: "legal", label: "Legal Documents", description: "Court filings, motions, and legal correspondence" },
      { id: "photos", label: "Photos & Evidence", description: "Photographic evidence and related visual documentation" },
      { id: "correspondence", label: "Correspondence", description: "Letters, emails, and other communications" },
      { id: "contracts", label: "Contracts", description: "Agreements, contracts, and related legal documents" },
      { id: "bills", label: "Billing & Expenses", description: "Medical bills, expenses, and financial records" },
      { id: "expert", label: "Expert Reports", description: "Reports and documentation from expert witnesses" },
      { id: "other", label: "Other Documents", description: "Miscellaneous documents that don't fit other categories" }
    ];

    // Get the user's tenant settings for potential custom document types
    const { data: tenantSettings, error } = await supabase
      .schema('tenants')
      .from('tenant_settings')
      .select('document_types')
      .eq('tenant_id', user.tenantId)
      .single<TenantSettings>();

    // If tenant has custom document types, add them to the list
    if (!error && tenantSettings?.document_types && tenantSettings.document_types.length > 0) {
      const customTypes = tenantSettings.document_types.map((type: DocumentType) => ({
        id: type.id,
        label: type.label,
        description: type.description || '',
        custom: true
      }));

      // Combine standard types with custom types
      return NextResponse.json({
        documentTypes: [...documentTypes, ...customTypes]
      });
    }

    // Return standard types only
    return NextResponse.json({ documentTypes });
  } catch (error: unknown) {
    console.error('Error fetching document types:', error);
    let errorMessage = 'Failed to fetch document types';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}, ['partner', 'attorney', 'paralegal', 'staff', 'client']); // Allow all authenticated roles
