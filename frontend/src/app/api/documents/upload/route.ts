// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth';
import { DocumentService } from '@/lib/services/document-service';
import { QueueService } from '@/lib/services/queue-service';
import { DocumentQueueItem } from '@/lib/services/queue-service';
import { withRateLimit } from '@/lib/middlewares/rate-limit-middleware';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define interfaces for document data
interface DocumentMetadata {
  title: string;
  description: string;
  document_type: string;
  case_id?: string;
  client_id?: string;
  status: 'draft' | 'final' | 'archived';
  is_sensitive: boolean;
  metadata: {
    original_upload_date: string;
    uploaded_from: string;
    priority: string;
  };
}

// Define interface for queue document params that matches DocumentQueueItem
interface QueueDocumentParams {
  documentId: string;
  userId: string;
  fileName: string;
  fileType: string;
  priority: 'high' | 'normal' | 'low';
  metadata: {
    document_type: string;
    case_id?: string;
    client_id?: string;
    description: string;
    has_deadlines: boolean;
    is_medical: boolean;
  };
}

interface ProcessingResult {
  status: string;
  jobId?: string;
  message: string;
}

export const POST = withRateLimit(
  async (
    req: NextRequest,
    user: AuthUser,
    supabase: SupabaseClient<Database, "public", any>,
    context: Record<string, unknown>
  ): Promise<Response> => {
    try {
      // Create document service with user's tenant context
      const documentService = new DocumentService(supabase, user.tenantId);

      // Get form data from request
      const formData = await req.formData();
      const file = formData.get('file') as File;

      if (!file) {
        return NextResponse.json({ error: 'No file provided' }, { status: 400 });
      }

      // Extract metadata from form
      const documentType = formData.get('documentType') as string || 'other';
      const description = formData.get('description') as string || '';
      const caseId = formData.get('caseId') as string | undefined; // Use undefined, not null
      const clientId = formData.get('clientId') as string | undefined;

      // Optional: Enable OCR analysis flag
      const enableOcr = formData.get('enableOcr') === 'true';
      const ocrType = formData.get('ocrType') as string || 'text';

      // Upload document with required metadata
      const metadata: DocumentMetadata = {
        title: file.name, // Use filename as title
        description: description,
        document_type: documentType,
        case_id: caseId, // Now correctly string | undefined
        client_id: clientId,
        status: 'draft',
        is_sensitive: false,
        metadata: {
          // Store extra metadata to help with document classification
          original_upload_date: new Date().toISOString(),
          uploaded_from: 'web_interface',
          priority: (formData.get('priority') as string) || 'normal',
        },
      };

      // Upload the document to GCS via our document service
      const document = await documentService.upload(file, metadata, user.id);

      // --- >>> START NEO4J ACTIVITY LOGGING <<< ---
      try {
        const activityTimestamp = new Date().toISOString();
        const activitySummary = `Uploaded document: ${file.name}`;
        const activityType = 'DOCUMENT_UPLOAD';

        const neo4jQuery = `
          MERGE (u:User {id: $userId})
          MERGE (at:ActivityType {name: $activityTypeName})
          CREATE (a:Activity {
              id: randomUUID(),
              type: $activityType,
              timestamp: datetime($timestamp),
              summary: $summary,
              document_id: $documentId,
              case_id: $caseId,
              tenant_id: $tenantId
          })
          CREATE (u)-[:PERFORMED]->(a)
          CREATE (a)-[:OF_TYPE]->(at)
          RETURN a.id
        `;

        const neo4jParams = {
          userId: user.id,
          activityTypeName: activityType,
          activityType: activityType,
          timestamp: activityTimestamp,
          summary: activitySummary,
          documentId: document.id,
          caseId: caseId, // Pass caseId (can be undefined)
          tenantId: user.tenantId
        };

        // TODO: Replace with actual call to Neo4j write tool/helper
        console.log('[Neo4j Logging] Would execute query:', neo4jQuery);
        console.log('[Neo4j Logging] With params:', neo4jParams);
        // Example placeholder: await callNeo4jWrite(neo4jQuery, neo4jParams);
        // If using direct MCP call: prepare tool call XML/JSON here

      } catch (neo4jError) {
        console.error('Failed to log document upload activity to Neo4j:', neo4jError);
        // Do not block the primary response if logging fails
      }
      // --- >>> END NEO4J ACTIVITY LOGGING <<< ---

      // Always enqueue document for automatic processing
      const queueService = new QueueService(supabase, user.tenantId);

      try {
        // Queue the document for automatic classification and processing
        const queueParams: QueueDocumentParams = {
          documentId: document.id,
          userId: user.id,
          fileName: file.name,
          fileType: file.type,
          priority: 'high', // Default to high priority for user-initiated uploads
          metadata: {
            document_type: documentType,
            case_id: caseId,
            client_id: clientId,
            description: description,
            // Include information to help with classification
            has_deadlines: documentType === 'legal' || documentType === 'correspondence',
            is_medical: documentType === 'medical',
          }
        };
        const jobId = await queueService.enqueueDocument(queueParams);

        console.log(`Document ${document.id} queued for processing with job ID ${jobId}`);

        // Indicate that processing was queued
        const analysisResult: ProcessingResult = {
          status: 'queued',
          jobId: jobId,
          message: 'Document queued for automatic processing'
        };

        return NextResponse.json({
          success: true,
          data: document,
          processing: {
            status: 'queued',
            jobId: analysisResult?.jobId,
            message: 'Document queued for automatic processing'
          }
        });
      } catch (processingError) {
        console.error('Error queueing document for processing:', processingError);
        // Still return success for the upload, but note the processing error
        return NextResponse.json({
          success: true,
          data: document,
          processing: {
            status: 'error',
            message: 'Document uploaded successfully, but could not be queued for processing'
          }
        });
      }
    } catch (error: unknown) {
      console.error('Document upload error:', error);

      // Extract error message safely
      let errorMessage = 'An unknown error occurred during upload';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Return a generic error response
      return NextResponse.json(
        { error: 'Failed to upload document', details: errorMessage },
        { status: 500 }
      );
    }
  },
);
