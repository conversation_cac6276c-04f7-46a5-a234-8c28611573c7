// @ts-nocheck - API route type issues
import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthUser } from '@/lib/auth';
import type { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';

// Define interfaces for summary data
interface DocumentSummary {
  id: string;
  document_id: string;
  document_type: string;
  summary_text: string;
  summarized_by: string;
  summarized_at: string;
  tenant_id: string;
}

interface SummaryRequestBody {
  document_id: string;
  document_type?: string;
  summary_text: string;
  summarized_by?: string;
}

/**
 * GET /api/documents/summary?document_id=...&document_type=...
 * Returns the summary for a document if it exists.
 */
export const GET = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  const { searchParams } = new URL(req.url);
  const document_id = searchParams.get('document_id');
  const document_type = searchParams.get('document_type');

  if (!document_id) {
    return NextResponse.json({ error: 'Missing document_id' }, { status: 400 });
  }

  // Query the summary table
  const { data, error } = await supabase
    .schema('tenants')
    .from('document_summaries')
    .select('*')
    .eq('document_id', document_id)
    .maybeSingle();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
  if (!data) {
    return NextResponse.json({ error: 'No summary found for this document.' }, { status: 404 });
  }

  return NextResponse.json({
    summaryText: data.summary_text,
    summarizedBy: data.summarized_by,
    summarizedAt: data.summarized_at,
    documentType: data.document_type,
  });
}, ['partner', 'attorney', 'paralegal', 'staff']);

/**
 * POST /api/documents/summary
 * Body: { document_id, document_type, summary_text, summarized_by }
 * Creates a new summary if one does not exist.
 */
export const POST = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const body = await req.json() as SummaryRequestBody;
    const { document_id, document_type, summary_text, summarized_by } = body;
    if (!document_id || !document_type || !summary_text) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }
    // Check if summary already exists
    const { data: existing, error: existingError } = await supabase
      .schema('tenants')
      .from('document_summaries')
      .select('id')
      .eq('document_id', document_id)
      .maybeSingle();
    if (existingError) {
      return NextResponse.json({ error: existingError.message }, { status: 500 });
    }
    if (existing) {
      return NextResponse.json({ error: 'Summary already exists for this document.' }, { status: 409 });
    }
    // Insert new summary
    const { error } = await supabase
      .schema('tenants')
      .from('document_summaries')
      .insert([
        {
          document_id,
          document_type,
          summary_text,
          summarized_by: summarized_by || user.email || user.id,
          summarized_at: new Date().toISOString(),
        },
      ]);
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    console.error('Error creating summary:', error);
    let errorMessage = 'Failed to create summary';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}, ['partner', 'attorney', 'paralegal', 'staff']);

/**
 * PATCH /api/documents/summary
 * Body: { document_id, summary_text, summarized_by }
 * Updates an existing summary.
 */
export const PATCH = withAuth(async (
  req: NextRequest,
  user: AuthUser,
  supabase: SupabaseClient<Database, "public", any>,
  context: Record<string, unknown>
): Promise<Response> => {
  try {
    const body = await req.json() as SummaryRequestBody;
    const { document_id, summary_text, summarized_by } = body;
    if (!document_id || !summary_text) {
      return NextResponse.json({ error: 'Missing required fields.' }, { status: 400 });
    }
    // Check if summary exists
    const { data: existing, error: existingError } = await supabase
      .schema('tenants')
      .from('document_summaries')
      .select('id')
      .eq('document_id', document_id)
      .maybeSingle();
    if (existingError) {
      return NextResponse.json({ error: existingError.message }, { status: 500 });
    }
    if (!existing) {
      return NextResponse.json({ error: 'No summary exists for this document.' }, { status: 404 });
    }
    // Update summary
    const { error } = await supabase
      .schema('tenants')
      .from('document_summaries')
      .update({
        summary_text,
        summarized_by: summarized_by || user.email || user.id,
        summarized_at: new Date().toISOString(),
      })
      .eq('document_id', document_id);
    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ success: true });
  } catch (error: unknown) {
    console.error('Error updating summary:', error);
    let errorMessage = 'Failed to update summary';
    if (error instanceof Error) {
      errorMessage = error.message;
    }
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}, ['partner', 'attorney', 'paralegal', 'staff']);
