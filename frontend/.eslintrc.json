{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:@typescript-eslint/strict", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "parserOptions": {"project": ["./tsconfig.json"], "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/consistent-type-imports": ["error", {"prefer": "type-imports"}], "@typescript-eslint/explicit-function-return-type": ["error", {"allowExpressions": true, "allowTypedFunctionExpressions": true}], "@typescript-eslint/ban-ts-comment": ["warn", {"ts-ignore": "allow-with-description", "minimumDescriptionLength": 10}], "react/react-in-jsx-scope": "off", "no-restricted-imports": ["error", {"paths": [{"name": "@/lib/auth-helpers", "message": "Use '@/lib/auth' instead. The auth-helpers module has been deprecated."}, {"name": "@/lib/auth-helpers/index", "message": "Use '@/lib/auth' instead. The auth-helpers module has been deprecated."}, {"name": "@/lib/auth/auth-helper", "message": "Use '@/lib/auth' instead. Import directly from the main auth module."}, {"name": "@/lib/auth/jwt-helper", "message": "Use '@/lib/auth' instead. Import directly from the main auth module."}]}]}, "overrides": [{"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "**/__tests__/**/*"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-function-return-type": "off"}}]}